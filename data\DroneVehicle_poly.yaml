

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: /media/test/4d846cae-2315-4928-8d1b-ca6d3a61a3c6/DroneVehicle   # dataset root dir
train: train/raw/images    # train images (relative to 'path') 
val: val/raw/images   # val images (relative to 'path') 
test: val/raw/images  # test images (optional)

# Classes
nc: 2  # number of classes
names: ['vehicle', 'None']  # class names


# Download script/URL (optional)
# download: https://ultralytics.com/assets/coco128.zip
