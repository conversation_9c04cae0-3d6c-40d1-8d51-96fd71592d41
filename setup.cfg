# Project-wide configuration file, can be used for package metadata and other toll configurations
# Example usage: global configuration for PEP8 (via flake8) setting or default pytest arguments

[metadata]
license_file = LICENSE
description-file = README.md


[tool:pytest]
norecursedirs =
    .git
    dist
    build
addopts =
    --doctest-modules
    --durations=25
    --color=yes


[flake8]
max-line-length = 120
exclude = .tox,*.egg,build,temp
select = E,W,F
doctests = True
verbose = 2
# https://pep8.readthedocs.io/en/latest/intro.html#error-codes
format = pylint
# see: https://www.flake8rules.com/
ignore =
    E731  # Do not assign a lambda expression, use a def
    F405
    E402
    F841
    E741
    F821
    E722
    F401
    W504
    E127
    W504
    E231
    E501
    F403
    E302
    F541


[isort]
# https://pycqa.github.io/isort/docs/configuration/options.html
line_length = 120
multi_line_output = 0
